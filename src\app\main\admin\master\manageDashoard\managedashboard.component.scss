:host {

    .content {

        form {
            width: 100%;
            // max-width: 800px !important;
        }

        .form-errors-model {
            flex: 1;

            code {
                background: none !important;
            }
        }

        .horizontal-stepper-wrapper,
        .vertical-stepper-wrapper {
            max-width: 800px;
        }

        .mat-stroked-button.mat-small {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
            margin-bottom: 33%;
        }

        .mat-stroked-button.mat-sm {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
        }

        .mat-flat-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .mat-stroked-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .h-30 {
            height: 30px !important;
            min-height: 30px;
        }

        table {
            width: 100%;
        }

        .mat-form-field.grid-search-field {
            // font-size: 14px;
            width: 50%;
            margin-left: 20px !important;
        }

        .blue-snackbar {
            background: #2196F3;
        }

    }
}
mat-form-field {
    width: 100%
  }
mat-grid-tile {
    background: lightblue;
}

.grid{
    position: absolute;
left: 24px;


background: #ecf2f7;
border-radius: 8px;
}

.Header{
position: absolute;
width: 60%;
height: 29px;
left: 10%;
top: 10%;
font-style: normal;
font-weight: 700;
font-size: 24px;
line-height: 29px;
/* identical to box height */

letter-spacing: 0.04em;
text-transform: capitalize;

color: #002D68;
}

.pheader{
    position: absolute;
width: 250px;
height: 30px;
left: 10%;
top: 30%;
font-style: normal;
font-weight: 300;
font-size: 12px;
line-height: 15px;
letter-spacing: 0.04em;

color: #000000;
}

.divarrow{
    box-sizing: border-box;

/* Auto layout */

display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

position: absolute;
width: 104px;
height: 44px;
left: 10%;
top: 70%;

border: 1px solid #002D68;
border-radius: 6px;
cursor: pointer;
}
.labletext
{
    width: 42px;
height: 19px;
font-style: normal;
font-weight: 500;
font-size: 16px;
line-height: 19px;

color: #002D68;


/* Inside auto layout */

flex: none;
order: 0;
flex-grow: 0;
cursor: pointer;
}
.rightarrow
{
    width: 32px;
height: 32px;


/* Inside auto layout */

flex: none;
order: 1;
flex-grow: 0;
margin-top: 10px;
color: #002D68;
cursor: pointer;
}
.img{
    position: absolute;
width: 30%;
//height: 40%;
left: 65%;
top: 15%;
cursor: pointer;
}