@import "src/@fuse/scss/fuse";

.login-wrapper {
    display: flex;
    height: 100vh;
    width: 100vw;
    align-items: center;
    justify-content: center;
}

.login-container {
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15), 0 1.5px 6px rgba(60, 60, 60, 0.08);
    background: #fff;
    overflow: hidden;
}

.login-left {
    flex: 1 0 auto;

    @include media-breakpoint('xs') {
        padding: 16px;
    }

    .login-form,
    .forgot-password-form {
        width: 400px;
        padding: 30px 50px;
        text-align: center;
        background-color: white;

        @include media-breakpoint('xs') {
            padding: 24px;
            width: 100%;
        }

        .logo {
            width: 150px;
            margin-bottom: 20px;
        }

        .sign-in-title {
            padding-bottom: 10px;
        }

        .sign-in-title,
        .forgot-password-title {
            font-size: xx-large;
            font-weight: bolder;
            text-align: start;
            letter-spacing: -1px;
        }

        .forgot-password-desc {
            text-align: start;
            font-weight: 600;
            padding-bottom: 20px;
        }

        form {
            width: 100%;
            text-align: left;

            mat-form-field {
                width: 100%;
            }

            .remember-forgot-password {
                font-size: 13px;
                margin-bottom: 8px;

                .forgot-password {
                    font-size: 13px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    cursor: pointer;
                }
            }

            .submit-button,
            .forgetPassword-button {
                width: 100%;
                height: 45px;
                border-radius: 20px;
                background-color: #10069F;
                color: white;

                @include media-breakpoint('xs') {
                    width: 90%;
                }
            }

            .forgetPassword-button {
                margin-top: 10px;
            }

            .return-to-signin {
                text-align: start;
                font-weight: 600;
                padding-top: 20px;

                a {
                    cursor: pointer;
                }
            }
        }
    }
}

.login-right {
    background-image: url("../../../assets/images/logos/landingPage_background.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;

    .login-header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 56px;
        padding: 0 32px 0 24px;
        box-sizing: border-box;
        border-radius: 12px 12px 0 0;

        .uems-title {
            color: #fff;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .uems-subtitle {
            text-align: right;

            div {
                color: #fff;
                font-size: 1rem;
                font-weight: 400;

                &.highlights {
                    color: #3ec6ff;
                }
            }
        }
    }

    .login-info {
        padding: 48px 40px;
        max-width: 600px;
        margin: auto;
        color: #fff;
        text-align: left;

        h2 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 18px;
            letter-spacing: -1.5px;
            line-height: 1.1;
        }

        .intro {
            font-size: 1.75rem;
            color: #b0bed0;
            margin-bottom: 0;
        }

        .modules {
            font-size: 1.25rem;
            color: #b0bed0;
            margin-bottom: 24px;

            strong {
                color: #fff;
                font-weight: 500;
                font-size: 1.5rem;
            }
        }

        .footer {
            margin-top: 32px;
            font-size: 1.3rem;
            color: #b0bed0;
            letter-spacing: 0.5px;
        }
    }
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 24px 0;

    .line {
        flex: 1;
        height: 1px;
        background: #e0e0e0;
    }

    .text {
        padding: 0 16px;
        color: #888;
        font-size: 14px;
    }
}

.social-login-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;

    button {
        width: 100px;
        border-radius: 20px;
        box-shadow: none !important;
        border: 1px solid #e0e0e0;
    }
}