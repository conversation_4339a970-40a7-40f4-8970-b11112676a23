import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { BroadcastService, MsalConfig } from '@azure/msal-angular';
import { MsalService } from '@azure/msal-angular';
import { FuseConfigService } from '@fuse/services/config.service';
import { fuseAnimations } from '@fuse/animations';
import { AuthService, GoogleLoginProvider } from 'angularx-social-login';
import { Router, ActivatedRoute } from '@angular/router';
import { UserLoginDto, ExternalLoginRequestDto } from 'app/dto/userDto';
import { BaseComponent } from '../common/base.component';
import { MatSnackBar } from '@angular/material';
import { UserApiService } from 'app/services/api/user.api.service';
import { environment } from 'environments/environment';



@Component({
    selector: 'login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'],
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations
})


export class LoginComponent extends BaseComponent {

    // msalConfig: MsalConfig = {
    //     clientID: '4c7f58aa-2412-4de3-b73b-8b13174e23df',
    //     authority: 'https://login.microsoftonline.com/common/',
    //     validateAuthority: true,
    //     redirectUri: 'http://localhost:4201/',
    //     cacheLocation: 'localStorage',
    //     postLogoutRedirectUri: 'http://localhost:4201/',
    //     navigateToLoginRequestUrl: true,
    // };

    loginBGUrl = environment.loginBGUrl;
    redirectUrl = '';
    loginForm: FormGroup;
    forgetPasswordForm: FormGroup;
    hpbsUserRoleList: any[];
    hpbsUrl = environment.hpbsUrl;
    hosUrl = environment.hosUrl;
    entrypassUrl = environment.entrypassUrl;
    isForgotPassword = false;
    constructor(
        private _fuseConfigService: FuseConfigService,
        private _formBuilder: FormBuilder,
        private _authService_ms: MsalService,
        private _router: Router,
        _snackBar: MatSnackBar,
        private _userApi: UserApiService,
        private _authService_google: AuthService,
        private _aroute: ActivatedRoute
    ) {

        super(_snackBar);
        // Configure the layout
        this._fuseConfigService.config = {
            layout: {
                navbar: {
                    hidden: true
                },
                toolbar: {
                    hidden: true
                },
                footer: {
                    hidden: true
                },
                sidepanel: {
                    hidden: true
                },

            }
        };
    }

    ngOnInit(): void {
        const url = this._aroute.snapshot.queryParamMap.get('action');
        const redirectUrl = this._aroute.snapshot.queryParamMap.get('redirectUrl');
        const proj = this._aroute.snapshot.queryParamMap.get('proj');

        this.loginForm = this._formBuilder.group({
            email: ['', Validators.required],
            password: ['', Validators.required]
        });

        this.forgetPasswordForm = this._formBuilder.group({
            email: ['', Validators.required],
        });

        localStorage.setItem('SSOADMIN', '');
        localStorage.setItem('SSOTOKEN', '');
        localStorage.setItem('SSOUSERNAME', '');
        localStorage.setItem('SSOUSERPIC', '');
        localStorage.setItem('SSOUSEREMAIL', '');
        localStorage.setItem('SSOUSERCOMPANYID', '');
        if (url === 'logout') {
            //   console.log(this._authService_google.signOut);
            //this._authService_google.signOut(true);
            this._authService_google.signOut();
            //  this._authService_google.readyState
            // this._authService_google.authState.subscribe((user) => {
            //     console.log(user);
            // });
        } else if (url === 'lo') {
            this.showErrorMsg('User session has timed out. Please login to the system.');
            if (typeof redirectUrl !== 'undefined' && redirectUrl !== null && redirectUrl.trim().length !== 0) {
                this.redirectUrl = redirectUrl;
            } else {
                this.redirectUrl = '';
            }

        }
        else {

            this._authService_google.authState.subscribe((user) => {
                if (user != null) {
                    const obj = this.wrapRequestObject(user);
                    this._userApi.loginExternal(obj)
                        .subscribe(
                            (data) => {
                                console.log(data);
                                // basic redirection for now
                                if (data.code === 1) {
                                    const objArray = JSON.parse(data.obj.toString());
                                    localStorage.setItem('SSOADMIN', '');
                                    localStorage.setItem('SSOTOKEN', '');
                                    localStorage.setItem('SSOUSERNAME', '');
                                    localStorage.setItem('SSOUSERPIC', '');
                                    localStorage.setItem('SSOROLEID', '');
                                    localStorage.setItem('SSOUSEREMAIL', '');
                                    localStorage.setItem('SSOUSERCOMPANYID', '');

                                    console.log(objArray);
                                    if (objArray['isAdmin']) {
                                        if (proj === 'hsk') {
                                            window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                                        }
                                        else if (proj === 'hos') {
                                            window.location.href = this.hosUrl + '/login?token=' + objArray['Jwt'];
                                        }
                                        else if (proj === 'ep') {
                                            window.location.href = this.entrypassUrl + '/login?token=' + objArray['Jwt'];
                                        }
                                        else if (proj === null) {
                                            localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                            localStorage.setItem('SSOADMIN', '1');
                                            localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                            localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                            localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                            localStorage.setItem('SSOUSERID', objArray['userId']);
                                            localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                            localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                            this._router.navigateByUrl('app/dashboard');
                                        }
                                        else {
                                            localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                            localStorage.setItem('SSOADMIN', '1');
                                            localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                            localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                            localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                            localStorage.setItem('SSOUSERID', objArray['userId']);
                                            localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                            localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                            this._router.navigateByUrl('app/dashboard');
                                        }
                                    }
                                    else if (redirectUrl.trim().length > 0) {
                                        ///yet to handle redirection
                                        window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];

                                    }
                                    else {
                                        window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                                    }
                                } else {
                                    this.showErrorMsg('Invalid username or password!');
                                }
                            },
                            _error => {
                            }
                        );
                }
            });
        }
    }
    login(): void {
        const proj = this._aroute.snapshot.queryParamMap.get('proj');
        const req: UserLoginDto = {
            password: this.loginForm.controls.password.value,
            email: this.loginForm.controls.email.value
        };
        const obj = this.wrapRequestObject(req);
        this._userApi.loginCustom(obj)
            .subscribe(
                (data) => {
                    console.log(data);
                    // basic redirection for now
                    if (data.code === 1) {
                        const objArray = JSON.parse(data.obj.toString());
                        localStorage.setItem('SSOADMIN', '');
                        localStorage.setItem('SSOTOKEN', '');
                        localStorage.setItem('SSOUSERNAME', '');
                        localStorage.setItem('SSOUSERPIC', '');
                        localStorage.setItem('SSOROLEID', '');
                        localStorage.setItem('SSOUSERID', '');
                        localStorage.setItem('SSOUSEREMAIL', '');
                        localStorage.setItem('SSOUSERCOMPANYID', '');

                        console.log(objArray);
                        if (objArray['isAdmin']) {
                            if (proj === 'hsk') {
                                window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                            }
                            else if (proj === 'hos') {
                                window.location.href = this.hosUrl + '/login?token=' + objArray['Jwt'];
                            }
                            else if (proj === 'ep') {
                                window.location.href = this.entrypassUrl + '/login?token=' + objArray['Jwt'];
                            }
                            else if (proj === null) {
                                localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                localStorage.setItem('SSOADMIN', '1');
                                localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                localStorage.setItem('SSOUSERID', objArray['userId']);
                                localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                this._router.navigateByUrl('app/dashboard');
                            }
                            else {
                                localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                localStorage.setItem('SSOADMIN', '1');
                                localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                localStorage.setItem('SSOUSERID', objArray['userId']);
                                localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                this._router.navigateByUrl('app/dashboard');
                            }

                        } else {
                            window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                        }
                    } else {
                        this.showErrorMsg('Invalid username or password!');
                    }
                },
                _error => {
                }
            );
    }

    async loginWithMS(): Promise<void> {
        // // MSAL login


        // const msalConfig = {
        //     auth: {
        //         //  clientId: 'e760cab2-b9a1-4c0d-86fb-ff7084abd902', // This is your client ID
        //         authority: 'https://fabrikamb2c.b2clogin.com/fabrikamb2c.onmicrosoft.com/b2c_1_susi', // This is your tenant info
        //         validateAuthority: false
        //     },
        //     cache: {
        //         cacheLocation: 'localStorage',
        //         storeAuthStateInCookie: true
        //     }
        // };
        // instantiate MSAL
        var isValidated = false;
        const proj = this._aroute.snapshot.queryParamMap.get('proj');
        const myMSALObj = await this._authService_ms.loginPopup(['user.read'])
            .then(function (loginResponse) {
                console.log(loginResponse);
                isValidated = true;
            }).catch(function (error) {
                console.log(error);
                isValidated = false;
            });
        if (isValidated) {
            const user = await this._authService_ms.getUser();
            if (user) {
                const req: ExternalLoginRequestDto = {
                    email: user.displayableId,
                    authToken: '',
                    firstName: '',
                    id: '',
                    idToken: '',
                    lastName: '',
                    name: user.name,
                    photoUrl: '',
                    provider: ''
                };
                const obj = this.wrapRequestObject(req);
                this._userApi.loginExternal(obj)
                    .subscribe(
                        (data) => {
                            console.log(data);
                            // basic redirection for now
                            if (data.code === 1) {
                                const objArray = JSON.parse(data.obj.toString());
                                localStorage.setItem('SSOADMIN', '');
                                localStorage.setItem('SSOTOKEN', '');
                                localStorage.setItem('SSOUSERNAME', '');
                                localStorage.setItem('SSOUSERID', '');
                                localStorage.setItem('SSOUSERPIC', '');
                                localStorage.setItem('SSOUSEREMAIL', '');
                                localStorage.setItem('SSOUSERCOMPANYID', '');
                                if (objArray['isAdmin']) {
                                    if (proj === 'hsk') {
                                        window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                                    }
                                    else if (proj === 'hos') {
                                        window.location.href = this.hosUrl + '/login?token=' + objArray['Jwt'];
                                    }
                                    else if (proj === 'ep') {
                                        window.location.href = this.entrypassUrl + '/login?token=' + objArray['Jwt'];
                                    }
                                    else if (proj === null) {
                                        localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                        localStorage.setItem('SSOADMIN', '1');
                                        localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                        localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                        localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                        localStorage.setItem('SSOUSERID', objArray['userId']);
                                        localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                        localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                        this._router.navigateByUrl('app/dashboard');
                                    }
                                    else {
                                        localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                        localStorage.setItem('SSOADMIN', '1');
                                        localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                        localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                        localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                        localStorage.setItem('SSOUSERID', objArray['userId']);
                                        localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                        localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                        this._router.navigateByUrl('app/dashboard');
                                    }

                                } else {
                                    window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                                }
                            } else {
                                this.showErrorMsg('Invalid username or password!');
                            }
                        },
                        _error => {
                        }
                    );
            }
        }

        // this._authService_ms = new MsalService(this._router, this._broadcastservice);
        // const requestObj = {
        //     scopes: ['user.read']
        // };
        // this._authService_ms.acquireTokenSilent(['user.read']).then(function (accessToken) {
        //     console.log(accessToken);
        // })
        // this._authService_ms.acquireTokenPopup(requestObj.scopes)
        //     .then(function (loginResponse) {
        //         console.log(loginResponse);
        //     }).catch(function (error) {
        //         console.log(error);
        //     });
    }

    loginWithGoogle(): void {
        /// google login
        const proj = this._aroute.snapshot.queryParamMap.get('proj');
        this._authService_google.signIn(GoogleLoginProvider.PROVIDER_ID).then((user) => {
            const obj = this.wrapRequestObject(user);
            this._userApi.loginExternal(obj)
                .subscribe(
                    (data) => {
                        console.log(data);
                        // basic redirection for now
                        if (data.code === 1) {
                            const objArray = JSON.parse(data.obj.toString());
                            localStorage.setItem('SSOADMIN', '');
                            localStorage.setItem('SSOTOKEN', '');
                            localStorage.setItem('SSOUSERNAME', '');
                            localStorage.setItem('SSOUSERPIC', '');
                            localStorage.setItem('SSOUSERID', '');
                            localStorage.setItem('SSOUSEREMAIL', '');
                            localStorage.setItem('SSOUSERCOMPANYID', '');

                            console.log(objArray);
                            if (objArray['isAdmin']) {
                                if (proj === 'hsk') {
                                    window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                                }
                                else if (proj === 'hos') {
                                    window.location.href = this.hosUrl + '/login?token=' + objArray['Jwt'];
                                }
                                else if (proj === 'ep') {
                                    window.location.href = this.entrypassUrl + '/login?token=' + objArray['Jwt'];
                                }
                                else if (proj === null) {
                                    localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                    localStorage.setItem('SSOADMIN', '1');
                                    localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                    localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                    localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                    localStorage.setItem('SSOUSERID', objArray['userId']);
                                    localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                    localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                    this._router.navigateByUrl('app/dashboard');
                                }
                                else {
                                    localStorage.setItem('SSOTOKEN', objArray['Jwt']);
                                    localStorage.setItem('SSOADMIN', '1');
                                    localStorage.setItem('SSOUSERNAME', objArray['userFullName']);
                                    localStorage.setItem('SSOUSERPIC', objArray['photoUrl']);
                                    localStorage.setItem('SSOROLEID', objArray['userRoleId']);
                                    localStorage.setItem('SSOUSERID', objArray['userId']);
                                    localStorage.setItem('SSOUSEREMAIL', objArray['userEmail']);
                                    localStorage.setItem('SSOUSERCOMPANYID', objArray['companyId']);
                                    this._router.navigateByUrl('app/dashboard');
                                }
                            } else {
                                window.location.href = this.hpbsUrl + '#/login?token=' + objArray['Jwt'];
                            }
                        } else {
                            this.showErrorMsg('Invalid username or password!');
                        }
                    },
                    _error => {
                    }
                );


        });
        //  this._authService_google.authState.subscribe((user) => {
        //     // this.user = user;
        //      //this.loggedIn = (user != null);
        //      console.log(user);
        //    });
    }
}
