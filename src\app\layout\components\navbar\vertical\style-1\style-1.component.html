<!-- <div class="navbar-top" [ngClass]="fuseConfig.layout.navbar.secondaryBackground">

    <div class="logo">
        <img class="logo-icon" src="../../../../../../assets/images/logos/UETrackLogo2020.png">
   </div>

</div>


<div class="navbar-scroll-container" [ngClass]="fuseConfig.layout.navbar.primaryBackground"
     fusePerfectScrollbar [fusePerfectScrollbarOptions]="{suppressScrollX: true}">

    <div class="user" fxLayout="column" [ngClass]="fuseConfig.layout.navbar.secondaryBackground">
        <div class="avatar-container" [ngClass]="fuseConfig.layout.navbar.primaryBackground">
            <img class="avatar" src="assets/images/avatars/Velazquez.jpg">
        </div>

        <div class="h3 username"><PERSON></div>
        <div class="h5 email hint-text mt-8"><EMAIL></div>
    </div>

    <div class="navbar-content">
        <fuse-navigation class="material2" layout="vertical"></fuse-navigation>
    </div>

</div> -->
<div style="display: flex; flex-direction: column;">
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 15px;">
        <div style="color: white; font-size: 28px; font-weight: bold;">UETrack™</div>
        <span>
            <mat-icon [matMenuTriggerFor]="userMenu" style="color: white; font-size: 28px;">account_circle</mat-icon>

            <mat-menu #userMenu="matMenu" [overlapTrigger]="false">

                <button mat-menu-item (click)="changepassword()">
                    <mat-icon>vpn_key</mat-icon>
                    <span>Change Password</span>
                </button>

                <button mat-menu-item class="" (click)="logout()">
                    <mat-icon>exit_to_app</mat-icon>
                    <span>Logout</span>
                </button>

            </mat-menu>
        </span>
    </div>
    <div
        style="display: flex; align-items: center; margin-top: 30px;  flex-direction: column; justify-content: center; gap: 10px;">
        <div class="avatar-wrapper">
            <img
                class="avatar"
                [src]="picUrl"
                (error)="onImageError()"
                (load)="onImageLoad()"
                [style.display]="showImage ? 'block' : 'none'">
            <div
                class="avatar-initials"
                [style.display]="showImage ? 'none' : 'flex'"
                [title]="userName">
                {{getInitials()}}
            </div>
        </div>
        <div style="display: flex; align-items: center;flex-direction: column; justify-content: center;">
            <span style="color: white; font-size: 18px; font-weight: 400;">{{userName}}</span>
            <span
                style="color: rgb(155, 155, 155); font-size: 16px; font-weight: 400; margin-top: -5px;">{{userEmail}}</span>
        </div>
    </div>
<div [ngClass]="fuseConfig.layout.navbar.primaryBackground">
    <fuse-navigation layout="horizontal"></fuse-navigation>
</div>
</div>