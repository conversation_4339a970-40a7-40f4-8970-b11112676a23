<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Dashboard</h2>
            </div>
        </div>
    </div>
    <div class="content p-24">
        <mat-grid-list cols="4" gutterSize="10px" rowHeight="200px">
            <mat-grid-tile *ngFor="let module of moduleList" class="grid" >
                <label class="Header">{{module.moduleDesc}}</label>
                <p class="pheader">{{module.Description}}</p>
                <div class="divarrow" (click)="navigateToHousekeeping(module.URL)"><label class="labletext">Open</label><mat-icon class="rightarrow">play_circle_filled</mat-icon></div>
                <img src='{{ "assets/images/logos/" + module.moduleDesc + ".png" }}' class="img" (click)="navigateToHousekeeping(module.URL)"/></mat-grid-tile>
          </mat-grid-list>
    </div>
</div>