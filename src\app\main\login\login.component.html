<div class="login-wrapper">
    <div class="login-container" fxLayout="row" fxLayoutAlign="stretch stretch">
        <div class="login-left" fxFlex="40" fxLayout="column" fxLayoutAlign="center end">
            <div class="login-form" *ngIf="!isForgotPassword">
                <div class="logo">
                    <img src="../../../assets/images/logos/UETrackLogo2020.png" alt="UETrack Logo" class="logo-image">
                </div>
                <div class="sign-in-title">Sign in</div>
                <form name="loginForm" [formGroup]="loginForm" (keyup.enter)="login()" novalidate>
                    <mat-form-field appearance="outline">
                        <mat-label>Email</mat-label>
                        <input matInput type="email" formControlName="email">
                        <mat-error *ngIf="loginForm.get('email').hasError('required')">
                            Email is required
                        </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>Password</mat-label>
                        <input matInput type="password" formControlName="password">
                        <mat-error>
                            Password is required
                        </mat-error>
                    </mat-form-field>

                    <div class="remember-forgot-password" fxLayout="row" fxLayout.xs="column" fxLayoutAlign="end center"
                        (click)="isForgotPassword = true">
                        <a class="forgot-password">Forgot Password?</a>
                    </div>

                    <button mat-raised-button type="submit" class="submit-button" aria-label="Sign in"
                        [disabled]="loginForm.invalid" (click)="login()">
                        Sign in
                    </button>
                </form>

                <div class="separator">
                    <div class="line"></div>
                    <span class="text">Or continue with</span>
                    <div class="line"></div>
                </div>

                <div class="social-login-buttons">
                    <button mat-raised-button class="google" (click)="loginWithGoogle()">
                        <mat-icon fontSet="fab" fontIcon="fa-google"></mat-icon>
                    </button>
                    <button mat-raised-button class="facebook" (click)="loginWithMS()">
                        <mat-icon fontSet="fab" fontIcon="fa-microsoft"></mat-icon>
                    </button>
                </div>
            </div>

            <div class="forgot-password-form" *ngIf="isForgotPassword">
                <div class="logo">
                    <img src="../../../assets/images/logos/UETrackLogo2020.png" alt="UETrack Logo" class="logo-image">
                </div>
                <div class="forgot-password-title">Forgot password?</div>
                <div class="forgot-password-desc">Fill the form to reset your password</div>

                <form name="forgetPasswordForm" [formGroup]="forgetPasswordForm" (keyup.enter)="forgetPassword()"
                    novalidate>
                    <mat-form-field appearance="outline">
                        <mat-label>Email</mat-label>
                        <input matInput type="email" formControlName="email">
                        <mat-error *ngIf="forgetPasswordForm.get('email').hasError('required')">
                            Email is required
                        </mat-error>
                    </mat-form-field>

                    <button mat-raised-button class="forgetPassword-button">Send reset link</button>
                    <div class="return-to-signin">Return to <a (click)="isForgotPassword = false">sign in</a></div>
                </form>
            </div>
        </div>

        <div class="login-right" fxFlex="60" fxLayout="column" fxLayoutAlign="center center">
            <div class="login-header">
                <span class="uems-title">UEMS</span>
                <div class="uems-subtitle">
                    <div>Empowering Spaces.</div>
                    <div class="highlights">Impacting Lives.</div>
                </div>
            </div>
            <div class="login-info">
                <h2>Welcome to<br>UETrack™</h2>
                <p class="intro">Access the complete digital ecosystem for smart operations. Log in once to seamlessly
                    navigate across all UETrack™ modules:</p>
                <p class="modules">
                    <strong>Entrypass</strong>,
                    <strong>Housekeeping</strong>,
                    <strong>MSA</strong>,
                    <strong>Training Management</strong>,
                    <strong>Store Inventory</strong>,
                    <strong>SmartToilet</strong>,
                    <strong>Hospitality</strong>,
                    <strong>ESG</strong>,
                    <span>and more.</span>
                </p>
                <div class="footer">Optimized for performance. Designed for productivity. Secured by UEMS.</div>
            </div>
        </div>
    </div>
</div>